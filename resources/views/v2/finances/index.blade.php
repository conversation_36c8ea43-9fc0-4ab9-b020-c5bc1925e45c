@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
<link rel="stylesheet" href="{{ asset('css/v2-finances.css') }}">
<!-- Select2 CSS for group dropdown -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
/* Custom Select2 styling for UK donations */
.select2-container--default .select2-selection--single {
    height: 52px !important;
    border: 1px solid #CCCCCC !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.1) !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 34px !important;
    padding-left: 0 !important;
    font-family: 'Inter', sans-serif !important;
    font-size: 14px !important;
    color: #333333 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 50px !important;
    right: 12px !important;
}

.select2-dropdown {
    border: 1px solid #CCCCCC !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-purple, #5144A1) !important;
}

/* Error state styling */
.v2-field-error {
    color: #ef4444;
    font-size: 14px;
    margin-top: 5px;
    display: none;
}

.error .select2-container--default .select2-selection--single {
    border-color: #ef4444 !important;
}
</style>
@endpush

@section('title', 'Finances')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Finances</h1>
    </div>

    <div class="v2-account-content">
        @if(session('success'))
            <div class="v2-alert v2-alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="v2-alert v2-alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Tab Navigation -->
        <div class="v2-finances-tabs">
            <button class="v2-tab-button {{ $activeTab === 'us' ? 'active' : '' }}" data-tab="us">
                United States
            </button>
            <button class="v2-tab-button {{ $activeTab === 'uk' ? 'active' : '' }}" data-tab="uk">
                United Kingdom
            </button>
            <button class="v2-tab-button {{ $activeTab === 'sa' ? 'active' : '' }}" data-tab="sa">
                South Africa
            </button>
        </div>

        <!-- Tab Content -->
        <div class="v2-tab-content">
            <!-- United States Tab -->
            <div id="us-tab" class="v2-tab-pane {{ $activeTab === 'us' ? 'active' : '' }}">
                @include('v2.finances.partials.united-states')
            </div>

            <!-- United Kingdom Tab -->
            <div id="uk-tab" class="v2-tab-pane {{ $activeTab === 'uk' ? 'active' : '' }}">
                @include('v2.finances.partials.united-kingdom')
            </div>

            <!-- South Africa Tab -->
            <div id="sa-tab" class="v2-tab-pane {{ $activeTab === 'sa' ? 'active' : '' }}">
                @include('v2.finances.partials.south-africa')
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Select2 JS for group dropdown -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.v2-tab-button');
    const tabPanes = document.querySelectorAll('.v2-tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');

            // Trigger custom event for tab change
            document.dispatchEvent(new CustomEvent('tabChanged', { detail: { tab: targetTab } }));
        });
    });

    // Initialize Select2 for UK tab when it becomes active
    document.addEventListener('tabChanged', function(e) {
        if (e.detail.tab === 'uk') {
            setTimeout(function() {
                if ($('#uk-group-select').length && !$('#uk-group-select').hasClass('select2-hidden-accessible')) {
                    $('#uk-group-select').select2({
                        placeholder: 'Choose a group',
                        allowClear: true,
                        ajax: {
                            url: '{{ route('api.groups.search') }}',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    q: params.term,
                                    page: params.page
                                };
                            },
                            processResults: function (data, params) {
                                return {
                                    results: data.results.map(function(item) {
                                        return {
                                            id: item.id,
                                            text: item.text + (item.location ? ' (' + item.location + ')' : '')
                                        };
                                    }),
                                    pagination: data.pagination
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 0,
                        templateResult: function(group) {
                            if (group.loading) return group.text;
                            return $('<span>' + group.text + '</span>');
                        }
                    });
                }
            }, 100);
        }
    });

    // Initialize Select2 if UK tab is active on page load
    if ($('#uk-tab').hasClass('active')) {
        setTimeout(function() {
            if ($('#uk-group-select').length && !$('#uk-group-select').hasClass('select2-hidden-accessible')) {
                $('#uk-group-select').select2({
                    placeholder: 'Choose a group',
                    allowClear: true,
                    ajax: {
                        url: '{{ route('api.groups.search') }}',
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                q: params.term,
                                page: params.page
                            };
                        },
                        processResults: function (data, params) {
                            return {
                                results: data.results.map(function(item) {
                                    return {
                                        id: item.id,
                                        text: item.text + (item.location ? ' (' + item.location + ')' : '')
                                    };
                                }),
                                pagination: data.pagination
                            };
                        },
                        cache: true
                    },
                    minimumInputLength: 0,
                    templateResult: function(group) {
                        if (group.loading) return group.text;
                        return $('<span>' + group.text + '</span>');
                    }
                });
            }
        }, 100);
    }
});
</script>
@endpush
