<div class="v2-finances-content">
    <div class="v2-finances-header">
        <h2 class="v2-finances-title">Donate - United Kingdom</h2>
    </div>

    <div class="v2-uk-intro">
        <p class="v2-intro-text">
            To make a contribution to Common Change UK and to make a contribution to a specific group account,
            you may either fill out the form below, or follow the instructions to create a Standing Order.
        </p>
    </div>

    <div class="v2-finances-grid">
        <!-- Left Column - Donation Widget -->
        <div class="v2-finances-form-section">
            <div class="v2-uk-donation-widget">
                <!-- Donorbox-style Widget -->
                <form id="uk-donation-form" method="POST" action="{{ route('v2.account.finances.donate.uk') }}">
                    @csrf
                    <div class="v2-donorbox-container">
                    <!-- Header with progress and lock -->
                    <div class="v2-donorbox-header">
                        <div class="v2-donorbox-title">Choose amount</div>
                        <div class="v2-donorbox-security">
                            <i class="fas fa-lock"></i>
                            <div class="v2-progress-dots">
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot active"></span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>

                    <!-- Hidden form fields -->
                    <input type="hidden" name="frequency" id="uk-frequency" value="one-time">
                    <input type="hidden" name="amount" id="uk-amount" value="">
                    <input type="hidden" name="gift_aid" id="uk-gift-aid" value="0">

                    <!-- Frequency Selection -->
                    <div class="v2-frequency-section">
                        <div class="v2-frequency-tabs">
                            <button type="button" class="v2-frequency-tab active" data-frequency="one-time">One-time</button>
                            <button type="button" class="v2-frequency-tab" data-frequency="monthly">Monthly</button>
                        </div>

                        <p class="v2-frequency-note">
                            You can log in to <span class="v2-link-text">edit your recurring donation</span> any time
                            <i class="fas fa-info-circle" title="More information"></i>
                        </p>
                    </div>

                    <!-- Amount Selection -->
                    <div class="v2-amount-section">
                        <div class="v2-amount-buttons">
                            <button type="button" class="v2-amount-btn" data-amount="20">£20</button>
                            <button type="button" class="v2-amount-btn" data-amount="50">£50</button>
                            <button type="button" class="v2-amount-btn" data-amount="100">£100</button>
                        </div>
                        <div id="amount-error" class="v2-field-error" style="display: none;"></div>

                        <div class="v2-custom-amount">
                            <input type="number" id="uk-custom-amount" class="v2-custom-input" placeholder="£ Custom Amount" min="1" step="0.01" value="{{ old('amount') }}">
                        </div>
                    </div>

                    <!-- Group Selection -->
                    <div class="v2-group-section">
                        <div class="v2-group-dropdown">
                            <label class="v2-group-label">Donate to This Group</label>
                            <select id="uk-group-select" class="v2-group-select" name="group_id">
                                <option value="">Choose a group</option>
                                @if(isset($userGroups) && $userGroups->count() > 0)
                                    @foreach($userGroups as $group)
                                        <option value="{{ $group->id }}" {{ old('group_id') == $group->id ? 'selected' : '' }}>
                                            {{ $group->name }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            <i class="fas fa-chevron-down v2-dropdown-arrow"></i>
                        </div>
                        <div id="group-error" class="v2-field-error" style="display: none;"></div>
                    </div>

                    <!-- Gift Aid Section -->
                    <div class="v2-gift-aid">
                        <div class="v2-gift-aid-header">
                            <div class="v2-gift-aid-logo">gift aid it</div>
                        </div>

                        <div class="v2-gift-aid-content">
                            <h4 class="v2-gift-aid-title">Boost your donations by 25% at no cost to you!</h4>

                            <div class="v2-checkbox-container">
                                <input type="checkbox" id="gift-aid-checkbox" class="v2-checkbox" {{ old('gift_aid') ? 'checked' : '' }}>
                                <label for="gift-aid-checkbox" class="v2-checkbox-label">
                                    Yes, I am a UK taxpayer and I would like Common Change UK to
                                    <span class="v2-link-text">reclaim the tax on all qualifying donations I have made</span>,
                                    as well as any future donations, until I notify them otherwise.
                                </label>
                            </div>

                            <p class="v2-gift-aid-disclaimer">
                                I understand that if I pay less Income Tax and/or Capital Gains tax than the amount of Gift Aid claimed on all my donations in that tax year it is my responsibility to pay any difference.
                            </p>
                        </div>
                    </div>

                    <!-- Next Button -->
                    <div class="v2-next-section">
                        <button type="submit" class="v2-next-btn">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <!-- Powered by Donorbox -->
                    <div class="v2-powered-section">
                        <span class="v2-powered-text">Powered by</span>
                        <span class="v2-donorbox-brand">Donorbox</span>
                    </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Right Column - Information -->
        <div class="v2-finances-info-section">
            <div class="v2-uk-info-card">
                <h3 class="v2-uk-info-title">NOTE</h3>

                <div class="v2-uk-info-content">
                    <p class="v2-uk-info-text">
                        If you have any questions or need support, please contact us at
                        <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>.
                    </p>

                    <p class="v2-uk-info-highlight">
                        <strong>Donations are typically processed within 5 business days.</strong>
                    </p>

                    <p class="v2-uk-info-text">
                        For more info see
                        <a href="https://commonchange.zendesk.com" target="_blank" class="v2-link">https://commonchange.zendesk.com</a><br>
                        Or contact us on
                        <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Note: Select2 initialization is handled in the main finances view

    // Frequency tab switching
    const frequencyTabs = document.querySelectorAll('.v2-frequency-tab');
    const frequencyInput = document.getElementById('uk-frequency');

    frequencyTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            frequencyTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            frequencyInput.value = this.getAttribute('data-frequency');
        });
    });

    // Amount button selection
    const amountButtons = document.querySelectorAll('.v2-amount-btn');
    const customInput = document.getElementById('uk-custom-amount');
    const amountInput = document.getElementById('uk-amount');

    if (amountButtons.length && customInput && amountInput) {
        amountButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                amountButtons.forEach(b => b.classList.remove('selected'));
                this.classList.add('selected');
                customInput.value = '';

                const amount = this.getAttribute('data-amount');
                amountInput.value = amount;
                customInput.placeholder = `£${amount}`;

                // Clear any amount errors
                const amountError = document.getElementById('amount-error');
                if (amountError) {
                    amountError.style.display = 'none';
                }
                customInput.style.borderColor = '';

                console.log('Amount button clicked:', amount);
            });
        });

        // Custom amount input
        customInput.addEventListener('input', function() {
            if (this.value) {
                amountButtons.forEach(b => b.classList.remove('selected'));
                amountInput.value = this.value;

                // Clear any amount errors
                const amountError = document.getElementById('amount-error');
                if (amountError) {
                    amountError.style.display = 'none';
                }
                this.style.borderColor = '';

                console.log('Custom amount entered:', this.value);
            } else {
                amountInput.value = '';
            }
        });

        // Validate amount on blur
        customInput.addEventListener('blur', function() {
            const amount = parseFloat(this.value);
            if (this.value && (isNaN(amount) || amount < 1)) {
                showFieldError('uk-custom-amount', 'Please enter a valid amount of £1.00 or more.');
            }
        });
    } else {
        console.error('UK donation form elements not found');
    }

    // Gift Aid checkbox
    const giftAidCheckbox = document.getElementById('gift-aid-checkbox');
    const giftAidInput = document.getElementById('uk-gift-aid');

    giftAidCheckbox.addEventListener('change', function() {
        giftAidInput.value = this.checked ? '1' : '0';
    });

    // Form submission validation
    const form = document.getElementById('uk-donation-form');
    form.addEventListener('submit', function(e) {
        // Clear previous errors
        const errorElements = document.querySelectorAll('.v2-field-error');
        errorElements.forEach(el => {
            if (el.style.display !== 'none') {
                el.style.display = 'none';
                el.textContent = '';
            }
        });

        let hasErrors = false;

        // Validate amount
        const amount = parseFloat(amountInput.value);
        if (!amount || amount <= 0) {
            showFieldError('uk-custom-amount', 'Please select or enter a donation amount.');
            hasErrors = true;
        } else if (amount < 1) {
            showFieldError('uk-custom-amount', 'Minimum donation amount is £1.00.');
            hasErrors = true;
        }

        // Validate frequency
        const frequency = frequencyInput.value;
        if (!frequency || !['one-time', 'monthly'].includes(frequency)) {
            showFieldError('uk-frequency', 'Please select a donation frequency.');
            hasErrors = true;
        }

        if (hasErrors) {
            e.preventDefault();
            // Scroll to first error
            const firstError = document.querySelector('.v2-field-error[style*="block"]');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });

    // Initialize form state
    customInput.placeholder = '£ Custom Amount';

    // Set initial gift aid state
    giftAidInput.value = giftAidCheckbox.checked ? '1' : '0';

    // Set initial amount if there's an old value
    if (customInput.value) {
        amountInput.value = customInput.value;
    }

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        if (!field) return;

        // Look for existing error div in the parent container
        let errorDiv = field.parentNode.querySelector('.v2-field-error');

        if (!errorDiv) {
            // Create new error div if it doesn't exist
            errorDiv = document.createElement('div');
            errorDiv.className = 'v2-field-error';
            errorDiv.style.color = '#ef4444';
            errorDiv.style.fontSize = '14px';
            errorDiv.style.marginTop = '5px';
            field.parentNode.appendChild(errorDiv);
        }

        // Show the error
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';

        // Add error styling to the field
        field.style.borderColor = '#ef4444';

        // Remove error styling when user starts typing
        field.addEventListener('input', function() {
            errorDiv.style.display = 'none';
            field.style.borderColor = '';
        }, { once: true });
    }
});
</script>
