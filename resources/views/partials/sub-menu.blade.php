<div class="submenu-container">
    <div class="submenu-header">
        <div class="submenu-title">
            @if(request()->is('v2/account*'))
                MY ACCOUNT
            @else
                ACTIVITY
            @endif
        </div>
        <!-- Submenu toggle is hidden as requested -->
        <div class="submenu-toggle" style="display: none;">
            <i class="fas fa-chevron-left"></i>
        </div>
    </div>

    <div class="submenu-section">
        @if(request()->is('group/*/issue/*') || request()->is('group/*/issues') || request()->routeIs('issue.*'))
            <!-- Issue Creation Flow Submenu -->
            <div class="submenu-item {{ request()->is('group/*/issue/create') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/issue/create" style="color: inherit; text-decoration: none;">Create Request</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('issue.confirm') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Review & Confirm</div>
            </div>

            <div class="submenu-item {{ request()->is('group/*/issues') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/issues" style="color: inherit; text-decoration: none;">All Requests</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('issue.show') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Request Details</div>
            </div>

            <div class="submenu-item {{ request()->is('group/*/discussions') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/discussions" style="color: inherit; text-decoration: none;">Discussions</a>
                </div>
            </div>
        @elseif(request()->is('group') && !request()->is('group/*'))
            <!-- Group Discovery Submenu -->
            <div class="submenu-item {{ (request()->is('group') && !request()->is('group/*')) ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.index') }}" style="color: inherit; text-decoration: none;">Discover Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('mygroups') }}" style="color: inherit; text-decoration: none;">My Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.create') }}" style="color: inherit; text-decoration: none;">Create Group</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Popular Groups</div>
            </div>
        @elseif(request()->is('mygroups'))
            <!-- My Groups Submenu -->
            <div class="submenu-item {{ request()->is('mygroups') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('mygroups') }}" style="color: inherit; text-decoration: none;">My Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.create') }}" style="color: inherit; text-decoration: none;">Create Group</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.index') }}" style="color: inherit; text-decoration: none;">Find Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Group Invitations</div>
            </div>
        @elseif(request()->is('group/create'))
            <!-- Group Creation Flow Submenu -->
            <div class="submenu-item {{ request()->is('group/create') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.create') }}" style="color: inherit; text-decoration: none;">Create Group</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('mygroups') }}" style="color: inherit; text-decoration: none;">My Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.index') }}" style="color: inherit; text-decoration: none;">Find Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Group Guidelines</div>
            </div>
        @elseif(request()->is('group/*/edit'))
            <!-- Group Edit Flow Submenu -->
            <div class="submenu-item {{ request()->is('group/*/edit') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Edit Group</div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    @php
                        $groupId = request()->route('group');
                    @endphp
                    <a href="{{ route('group.show', $groupId) }}" style="color: inherit; text-decoration: none;">Group Home</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('group.issues', $groupId) }}" style="color: inherit; text-decoration: none;">View Requests</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Group Settings</div>
            </div>
        @elseif(request()->is('group/*') && !request()->is('group/*/issue/*') && !request()->is('group/*/issues'))
            <!-- Group Home Submenu -->
            <div class="submenu-item {{ request()->is('group/*') && !request()->is('group/*/issue/*') && !request()->is('group/*/issues') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}" style="color: inherit; text-decoration: none;">Group Activity</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/issues" style="color: inherit; text-decoration: none;">View Requests</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/issue/create" style="color: inherit; text-decoration: none;">Create Request</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->is('group/*/discussions') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="/group/{{ Auth::user()->get_current_group()->id }}/discussions" style="color: inherit; text-decoration: none;">Discussions</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Donate</div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">Transaction History</div>
            </div>
        @elseif(request()->is('v2/account*') || request()->is('v2/items*'))
            <!-- Account Management Submenu with organized sections -->

            <!-- MY ACCOUNT Section -->
            <div class="submenu-section-header">MY ACCOUNT</div>
            <div class="submenu-item {{ request()->routeIs('v2.account.security') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.security') }}" style="color: inherit; text-decoration: none;">Account Security</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('v2.account.contact') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.contact') }}" style="color: inherit; text-decoration: none;">Contact Info</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('v2.account.social') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.social') }}" style="color: inherit; text-decoration: none;">Social Media</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('v2.account.notifications') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.notifications') }}" style="color: inherit; text-decoration: none;">Notifications</a>
                </div>
            </div>

            <!-- FINANCES Section -->
            <div class="submenu-section-header">FINANCES</div>
            <div class="submenu-item {{ request()->routeIs('v2.account.finances') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.finances') }}" style="color: inherit; text-decoration: none;">Donate</a>
                </div>
            </div>

            <div class="submenu-item {{ request()->routeIs('v2.account.finances.donation-history') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.account.finances.donation-history') }}" style="color: inherit; text-decoration: none;">Donation History</a>
                </div>
            </div>

            <!-- ITEMS Section -->
            <div class="submenu-section-header">ITEMS</div>
            <div class="submenu-item {{ request()->routeIs('v2.items.*') || request()->routeIs('v2.skills.*') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('v2.items.index') }}" style="color: inherit; text-decoration: none;">My Items & Skills</a>
                </div>
            </div>

            <!-- SPREAD THE WORD Section -->
            <div class="submenu-section-header">SPREAD THE WORD</div>
            <div class="submenu-item {{ request()->routeIs('invite.create') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('invite.create') }}" style="color: inherit; text-decoration: none;">Invite Friends</a>
                </div>
            </div>
        @else
            <!-- Default Dashboard Submenu -->
            <div class="submenu-item {{ request()->is('dashboard*') ? 'active' : '' }}">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('dashboard.new', ['user_id' => Auth::id()]) }}" style="color: inherit; text-decoration: none;">Dashboard</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('mygroups') }}" style="color: inherit; text-decoration: none;">My Groups</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('issue.index') }}" style="color: inherit; text-decoration: none;">All Requests</a>
                </div>
            </div>

            <div class="submenu-item">
                <div class="submenu-icon"></div>
                <div class="submenu-text">
                    <a href="{{ route('discussion.index') }}" style="color: inherit; text-decoration: none;">All Discussions</a>
                </div>
            </div>
        @endif
    </div>
</div>
